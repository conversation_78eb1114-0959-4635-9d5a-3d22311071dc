"""
Vanna配置文件 - Gemini Balance版本
使用自部署的gemini-balance服务调用Gemini 2.0 Flash模型
"""

# Gemini Balance配置
GEMINI_BALANCE_CONFIG = {
    'base_url': 'http://localhost:8080/',  # 你的gemini-balance服务地址
    'api_key': '',  # 如果需要API密钥，请填写
    'model': 'gemini-2.0-flash-exp',  # 或者 'gemini-2.0-flash-thinking-exp'
    'temperature': 0.1,  # 较低的温度以获得更一致的结果
    'max_tokens': 4096,  # 最大输出tokens
    'timeout': 60,  # 请求超时时间（秒）
}

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',  # 如果Docker在其他地址，请修改
    'port': 3306,
    'user': 'root',
    'password': '123qwe',
    'dbname': 'whodb_test'  # 注意：这里是dbname，不是database
}

# ChromaDB配置
CHROMADB_CONFIG = {
    'path': './chroma_db_gemini',  # 使用不同的存储路径
    'client': 'persistent',  # 使用持久化存储
    'n_results': 10,  # 检索结果数量
}

# 其他配置
GENERAL_CONFIG = {
    'language': 'zh-CN',  # 响应语言
    'max_tokens': 14000,  # 最大token数
    'dialect': 'MySQL',  # SQL方言
}

# 可选的模型配置
AVAILABLE_MODELS = {
    'gemini-2.0-flash-exp': {
        'description': 'Gemini 2.0 Flash实验版本，速度快，适合一般查询',
        'max_tokens': 8192,
        'recommended_temperature': 0.1
    },
    'gemini-2.0-flash-thinking-exp': {
        'description': 'Gemini 2.0 Flash思考版本，推理能力更强，适合复杂查询',
        'max_tokens': 32768,
        'recommended_temperature': 0.2
    },
    'gemini-1.5-pro': {
        'description': 'Gemini 1.5 Pro，平衡性能和质量',
        'max_tokens': 8192,
        'recommended_temperature': 0.1
    }
}
