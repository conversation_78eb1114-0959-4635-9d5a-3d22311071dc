"""
Vanna配置文件
可以根据你的环境修改这些设置
"""

# Ollama配置
OLLAMA_CONFIG = {
    'model': 'qwen3:0.6b',  # 使用你已部署的qwen3模型
    'ollama_host': 'http://localhost:11434',  # Ollama服务地址
    'ollama_timeout': 300.0,  # 超时时间（秒）
    'keep_alive': '5m',  # 模型在内存中保持时间
    'options': {
        'num_ctx': 4096,  # 上下文长度
        'temperature': 0.1,  # 温度参数，越低越确定性
        'top_p': 0.9,  # Top-p采样
        'repeat_penalty': 1.1,  # 重复惩罚
    }
}

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',  # 如果Docker在其他地址，请修改
    'port': 3306,
    'user': 'root',
    'password': '123qwe',
    'dbname': 'whodb_test'  # 注意：这里是dbname，不是database
}

# ChromaDB配置
CHROMADB_CONFIG = {
    'path': './chroma_db',  # 数据存储路径
    'client': 'persistent',  # 使用持久化存储
    'n_results': 10,  # 检索结果数量
}

# 其他配置
GENERAL_CONFIG = {
    'language': 'zh-CN',  # 响应语言
    'max_tokens': 14000,  # 最大token数
    'dialect': 'MySQL',  # SQL方言
}
