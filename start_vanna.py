#!/usr/bin/env python3
"""
Vanna启动脚本 - 使用Ollama模型和MySQL数据库
"""

import os
import sys
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from vanna.ollama.ollama import Ollama
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# 导入配置
try:
    from config import OLLAMA_CONFIG, MYSQL_CONFIG, CHROMADB_CONFIG, GENERAL_CONFIG
except ImportError:
    print("⚠️ 未找到config.py文件，使用默认配置")
    OLLAMA_CONFIG = {
        'model': 'llama3.2:latest',
        'ollama_host': 'http://localhost:11434',
        'ollama_timeout': 300.0,
        'keep_alive': '5m',
        'options': {'num_ctx': 4096, 'temperature': 0.1}
    }
    MYSQL_CONFIG = {
        'host': 'localhost', 'port': 3306, 'user': 'root',
        'password': '123qwe', 'database': 'whodb_test'
    }
    CHROMADB_CONFIG = {'path': './chroma_db', 'client': 'persistent'}
    GENERAL_CONFIG = {'language': 'zh-CN', 'dialect': 'MySQL'}


class VannaOllama(ChromaDB_VectorStore, Ollama):
    """
    结合ChromaDB向量存储和Ollama LLM的Vanna类
    """
    def __init__(self, config=None):
        if config is None:
            config = {}

        # 初始化ChromaDB向量存储
        ChromaDB_VectorStore.__init__(self, config=config)

        # 初始化Ollama LLM
        Ollama.__init__(self, config=config)

    def extract_sql(self, llm_response):
        """
        改进的SQL提取方法，支持更多SQL语句类型
        """
        import re

        # 清理响应
        llm_response = llm_response.replace("\\_", "_")
        llm_response = llm_response.replace("\\", "")

        # 首先尝试提取```sql代码块
        sql_block = re.search(r"```sql\s*\n(.*?)```", llm_response, re.DOTALL | re.IGNORECASE)
        if sql_block:
            return sql_block.group(1).strip()

        # 尝试提取常见的SQL语句
        sql_patterns = [
            r'\b(SELECT\s+.*?)(?=;|\n\n|$)',
            r'\b(SHOW\s+.*?)(?=;|\n\n|$)',
            r'\b(INSERT\s+.*?)(?=;|\n\n|$)',
            r'\b(UPDATE\s+.*?)(?=;|\n\n|$)',
            r'\b(DELETE\s+.*?)(?=;|\n\n|$)',
            r'\b(CREATE\s+.*?)(?=;|\n\n|$)',
            r'\b(DROP\s+.*?)(?=;|\n\n|$)',
            r'\b(ALTER\s+.*?)(?=;|\n\n|$)',
            r'\b(WITH\s+.*?)(?=;|\n\n|$)',
        ]

        for pattern in sql_patterns:
            match = re.search(pattern, llm_response, re.IGNORECASE | re.DOTALL)
            if match:
                sql = match.group(1).strip()
                # 移除可能的分号
                if sql.endswith(';'):
                    sql = sql[:-1]
                return sql

        # 如果都没找到，返回原始响应
        return llm_response.strip()


def setup_vanna() -> VannaOllama:
    """
    设置和配置Vanna实例

    Returns:
        配置好的VannaOllama实例
    """

    # 合并配置
    config = {**OLLAMA_CONFIG, **CHROMADB_CONFIG, **GENERAL_CONFIG}
    
    print(f"正在初始化Vanna，使用模型: {config['model']}")
    print(f"Ollama服务地址: {config['ollama_host']}")
    print(f"ChromaDB存储路径: {config['path']}")

    # 创建Vanna实例
    vn = VannaOllama(config=config)

    print(f"正在连接到MySQL数据库...")
    print(f"主机: {MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}")
    print(f"数据库: {MYSQL_CONFIG['dbname']}")
    print(f"用户: {MYSQL_CONFIG['user']}")

    # 连接到MySQL数据库
    try:
        vn.connect_to_mysql(**MYSQL_CONFIG)
        print("✅ 成功连接到MySQL数据库!")
    except Exception as e:
        print(f"❌ 连接MySQL数据库失败: {e}")
        print("请确保:")
        print("1. MySQL服务正在运行")
        print("2. 数据库连接信息正确")
        print("3. 已安装PyMySQL: pip install PyMySQL")
        raise
    
    return vn


def train_on_database_schema(vn: VannaOllama):
    """
    在数据库模式上训练Vanna
    """
    print("\n正在获取数据库模式信息...")
    
    try:
        # 获取所有表的信息
        tables_info = vn.run_sql("SHOW TABLES")
        print(f"发现 {len(tables_info)} 个表")
        
        # 为每个表获取DDL并训练
        for _, row in tables_info.iterrows():
            table_name = row.iloc[0]  # 第一列是表名
            print(f"正在处理表: {table_name}")
            
            try:
                # 获取表结构
                create_table = vn.run_sql(f"SHOW CREATE TABLE `{table_name}`")
                if not create_table.empty:
                    ddl = create_table.iloc[0, 1]  # CREATE TABLE语句在第二列
                    vn.train(ddl=ddl)
                    print(f"  ✅ 已训练表结构: {table_name}")
                
                # 获取表的一些示例数据用于训练
                sample_data = vn.run_sql(f"SELECT * FROM `{table_name}` LIMIT 3")
                if not sample_data.empty:
                    print(f"  📊 表 {table_name} 有 {len(sample_data.columns)} 列，{len(sample_data)} 行示例数据")
                
            except Exception as e:
                print(f"  ⚠️ 处理表 {table_name} 时出错: {e}")
                continue
        
        print("\n✅ 数据库模式训练完成!")
        
    except Exception as e:
        print(f"❌ 获取数据库模式失败: {e}")


def interactive_mode(vn: VannaOllama):
    """
    交互式问答模式
    """
    print("\n" + "="*60)
    print("🤖 Vanna AI SQL助手已启动!")
    print("你可以用自然语言询问数据库相关问题")
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'help' 查看帮助")
    print("="*60)
    
    while True:
        try:
            question = input("\n💬 请输入你的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            
            if question.lower() == 'help':
                print("\n📖 帮助信息:")
                print("- 你可以用中文或英文提问")
                print("- 例如: '显示所有表'")
                print("- 例如: '用户表有多少条记录?'")
                print("- 例如: 'What are the top 10 users?'")
                continue
            
            if not question:
                continue
            
            print(f"\n🤔 正在思考: {question}")
            print("-" * 40)
            
            # 生成SQL
            sql = vn.generate_sql(question)
            print(f"📝 生成的SQL:\n{sql}")
            
            # 执行SQL
            print("\n⚡ 正在执行查询...")
            df = vn.run_sql(sql)
            
            if df is not None and not df.empty:
                print(f"\n📊 查询结果 ({len(df)} 行):")
                print(df.to_string(index=False))
                
                # 生成结果摘要
                try:
                    summary = vn.generate_summary(question, df)
                    print(f"\n📋 结果摘要:\n{summary}")
                except Exception as e:
                    print(f"⚠️ 生成摘要时出错: {e}")
            else:
                print("📭 查询无结果")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见!")
            break
        except Exception as e:
            print(f"\n❌ 处理问题时出错: {e}")
            print("请尝试重新表述你的问题")


def main():
    """
    主函数
    """
    print("🚀 启动Vanna AI SQL助手...")
    
    try:
        # 设置Vanna
        vn = setup_vanna()
        
        # 训练数据库模式
        train_on_database_schema(vn)
        
        # 进入交互模式
        interactive_mode(vn)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
