# 🤖 Vanna AI SQL助手 - Gemini Balance版本

这是一个配置好的Vanna AI SQL助手，使用你自部署的gemini-balance服务调用Gemini 2.0 Flash模型和MySQL数据库。

## 🆕 新功能特性

- ✅ 支持自部署的gemini-balance服务
- ✅ 使用Gemini 2.0 Flash模型（更强的推理能力）
- ✅ 改进的SQL提取算法
- ✅ 独立的配置和存储
- ✅ 完整的连接测试工具

## 📋 前置要求

### 1. Gemini Balance服务
确保你的gemini-balance服务正在运行：
```bash
# 检查服务状态
curl http://localhost:8080/v1/models

# 或者检查健康状态
curl http://localhost:8080/health
```

### 2. 环境要求
- Python 3.8+
- MySQL数据库 (Docker或本地)
- 你的gemini-balance服务地址

## 🚀 快速开始

### 1. 配置设置

编辑 `config_gemini.py` 文件：

```python
# Gemini Balance配置
GEMINI_BALANCE_CONFIG = {
    'base_url': 'http://localhost:8080/',  # 你的gemini-balance服务地址
    'api_key': '',  # 如果需要API密钥，请填写
    'model': 'gemini-2.0-flash-exp',  # 或其他支持的模型
    'temperature': 0.1,
    'max_tokens': 4096,
    'timeout': 60,
}

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123qwe',
    'dbname': 'whodb_test'
}
```

### 2. 测试连接

```bash
# 运行连接测试
python3 test_gemini_balance.py
```

这会测试：
- ✅ Gemini Balance服务连接
- ✅ MySQL数据库连接  
- ✅ SQL生成功能

### 3. 启动Vanna

```bash
# 启动Gemini Balance版本
python3 start_vanna_gemini.py
```

## 🎯 支持的模型

在 `config_gemini.py` 中可以选择不同的模型：

```python
# 可用模型选项
'gemini-2.0-flash-exp'          # 速度快，适合一般查询
'gemini-2.0-flash-thinking-exp' # 推理能力强，适合复杂查询  
'gemini-1.5-pro'                # 平衡性能和质量
```

## 💬 使用示例

启动后，你可以：

```
💬 请输入你的问题: 用户表有多少条记录？

💬 请输入你的问题: 显示销售额最高的5个产品

💬 请输入你的问题: 哪个用户的订单最多？

💬 请输入你的问题: SELECT * FROM users WHERE age > 25
```

## 🔧 高级配置

### 模型参数调优

```python
GEMINI_BALANCE_CONFIG = {
    'model': 'gemini-2.0-flash-thinking-exp',  # 使用思考版本
    'temperature': 0.2,     # 稍高的创造性
    'max_tokens': 8192,     # 更长的输出
    'timeout': 120,         # 更长的超时时间
}
```

### 向量存储配置

```python
CHROMADB_CONFIG = {
    'path': './chroma_db_gemini',  # 独立存储路径
    'n_results': 15,               # 更多检索结果
}
```

## 🔍 故障排除

### 1. Gemini Balance连接失败

```bash
# 检查服务状态
curl http://localhost:8080/v1/models

# 检查配置
python3 test_gemini_balance.py
```

常见问题：
- 服务地址错误（检查端口和协议）
- 服务未启动
- API密钥配置错误

### 2. SQL生成质量问题

尝试不同的模型：
```python
# 对于复杂查询，使用思考版本
'model': 'gemini-2.0-flash-thinking-exp'

# 调整温度参数
'temperature': 0.1  # 更确定性的输出
```

### 3. 性能优化

```python
# 减少检索结果数量
CHROMADB_CONFIG = {
    'n_results': 5,  # 减少到5个
}

# 调整超时时间
GEMINI_BALANCE_CONFIG = {
    'timeout': 30,  # 减少到30秒
}
```

## 📊 与Ollama版本的对比

| 特性 | Ollama版本 | Gemini Balance版本 |
|------|------------|-------------------|
| 模型 | qwen3:0.6b | gemini-2.0-flash-exp |
| 推理能力 | 基础 | 强大 |
| 复杂查询 | 有限 | 优秀 |
| 中文支持 | 良好 | 优秀 |
| 响应速度 | 快 | 中等 |
| 资源占用 | 本地GPU/CPU | 远程API |

## 🎛️ 命令参考

### 交互命令
- `help` - 显示帮助信息
- `model` - 显示当前模型信息
- `quit` / `exit` - 退出程序

### 测试命令
```bash
# 完整连接测试
python3 test_gemini_balance.py

# 仅测试MySQL
python3 -c "from test_gemini_balance import test_mysql_connection; test_mysql_connection()"
```

## 📁 文件结构

```
vanna-main-2/
├── start_vanna_gemini.py       # Gemini Balance启动脚本
├── config_gemini.py            # Gemini Balance配置文件
├── test_gemini_balance.py      # 连接测试脚本
├── src/vanna/gemini_balance/   # Gemini Balance集成代码
│   ├── __init__.py
│   └── gemini_balance_chat.py
├── chroma_db_gemini/           # 独立的向量存储
└── README_GEMINI.md            # 本文档
```

## 🎉 开始使用

1. **配置服务地址**: 编辑 `config_gemini.py`
2. **测试连接**: 运行 `python3 test_gemini_balance.py`  
3. **启动Vanna**: 运行 `python3 start_vanna_gemini.py`
4. **开始提问**: 用自然语言查询你的数据库！

现在你可以享受更强大的AI模型带来的更准确的SQL生成和更好的中文理解能力！🚀
