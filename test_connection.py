#!/usr/bin/env python3
"""
测试Ollama和MySQL连接
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ollama():
    """测试Ollama连接"""
    print("🔍 测试Ollama连接...")
    try:
        import ollama
        client = ollama.Client("http://localhost:11434")
        
        # 测试连接
        models = client.list()
        print(f"✅ Ollama连接成功!")
        print(f"📋 可用模型: {[m['model'] for m in models.get('models', [])]}")
        
        # 测试模型
        response = client.chat(
            model='qwen3:0.6b',
            messages=[{'role': 'user', 'content': 'Hello, can you help me with SQL?'}],
            stream=False
        )
        print(f"🤖 模型响应: {response['message']['content'][:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        return False

def test_mysql():
    """测试MySQL连接"""
    print("\n🔍 测试MySQL连接...")
    try:
        import pymysql
        
        conn = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123qwe',
            database='whodb_test',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✅ MySQL连接成功!")
        
        # 测试查询
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📊 数据库中有 {len(tables)} 个表")
            if tables:
                print(f"📋 表列表: {[list(t.values())[0] for t in tables[:5]]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请确保:")
        print("1. MySQL服务正在运行")
        print("2. 数据库 'whodb_test' 存在")
        print("3. 用户 'root' 密码为 '123qwe'")
        return False

def main():
    print("🧪 Vanna连接测试")
    print("=" * 40)
    
    ollama_ok = test_ollama()
    mysql_ok = test_mysql()
    
    print("\n" + "=" * 40)
    if ollama_ok and mysql_ok:
        print("🎉 所有连接测试通过!")
        print("🚀 可以运行: python start_vanna.py")
    else:
        print("⚠️ 部分连接测试失败，请检查配置")
        
if __name__ == "__main__":
    main()
