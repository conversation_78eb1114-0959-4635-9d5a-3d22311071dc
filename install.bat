@echo off
chcp 65001 >nul
echo 🚀 Vanna AI SQL助手安装脚本
echo ================================

REM 检查Python版本
echo 📋 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version') do echo ✅ 找到Python: %%i
)

REM 检查pip
echo 📋 检查pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到pip，请先安装pip
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('pip --version') do echo ✅ 找到pip: %%i
)

REM 询问是否创建虚拟环境
set /p create_venv="🤔 是否创建Python虚拟环境? (推荐) [y/N]: "
if /i "%create_venv%"=="y" (
    echo 📦 创建虚拟环境...
    python -m venv vanna_env
    echo 🔄 激活虚拟环境...
    call vanna_env\Scripts\activate.bat
    echo ✅ 虚拟环境已创建并激活
    echo 💡 下次使用前请运行: vanna_env\Scripts\activate.bat
)

REM 安装依赖
echo 📦 安装Python依赖...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Python依赖安装失败
    pause
    exit /b 1
) else (
    echo ✅ Python依赖安装完成
)

REM 检查Ollama
echo 📋 检查Ollama服务...
curl -s http://localhost:11434/api/version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Ollama服务未运行或未安装
    echo 请确保:
    echo 1. 已安装Ollama: https://ollama.ai/
    echo 2. Ollama服务正在运行: ollama serve
    echo 3. 已下载模型: ollama pull llama3.2
) else (
    echo ✅ Ollama服务正在运行
    echo 📋 检查可用模型...
    ollama list
)

REM 检查MySQL连接
echo 📋 检查MySQL连接...
python -c "import pymysql; conn = pymysql.connect(host='localhost', port=3306, user='root', password='123qwe', database='whodb_test'); print('✅ MySQL连接成功'); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ MySQL连接失败
    echo 请确保:
    echo 1. MySQL服务正在运行
    echo 2. 数据库whodb_test存在
    echo 3. 用户root密码为123qwe
)

echo.
echo 🎉 安装完成!
echo ================================
echo 🚀 启动命令: python start_vanna.py
echo ⚙️ 配置文件: config.py
echo 📚 依赖文件: requirements.txt
echo.
echo 💡 使用提示:
echo - 首次运行会自动训练数据库模式
echo - 可以用中文或英文提问
echo - 输入'help'查看帮助
echo - 输入'quit'退出
echo.
pause
