import json
import re
import requests
from typing import List, Dict, Any

from ..base import VannaBase
from ..exceptions import DependencyError


class GeminiBalanceChat(VannaBase):
    """
    Gemini Balance Chat implementation for Vanna
    支持通过自部署的gemini-balance服务调用Gemini模型
    """
    
    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)
        
        if config is None:
            config = {}
        
        # 必需的配置
        self.base_url = config.get("base_url")
        if not self.base_url:
            raise ValueError("base_url is required for GeminiBalanceChat")
        
        # 确保base_url以/结尾
        if not self.base_url.endswith('/'):
            self.base_url += '/'
        
        # API配置
        self.api_key = config.get("api_key", "")
        self.model = config.get("model", "gemini-2.0-flash-exp")
        self.temperature = config.get("temperature", 0.1)
        self.max_tokens = config.get("max_tokens", 4096)
        self.timeout = config.get("timeout", 60)
        
        # 请求头
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Vanna-GeminiBalance/1.0"
        }
        
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
        
        # 测试连接
        self._test_connection()
    
    def _test_connection(self):
        """测试与gemini-balance服务的连接"""
        try:
            # 尝试发送一个简单的测试请求
            test_payload = {
                "model": self.model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            response = requests.post(
                f"{self.base_url}v1/chat/completions",
                headers=self.headers,
                json=test_payload,
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"⚠️ Gemini Balance连接测试失败: HTTP {response.status_code}")
                print(f"响应: {response.text}")
            else:
                print(f"✅ Gemini Balance连接成功! 模型: {self.model}")
                
        except Exception as e:
            print(f"⚠️ Gemini Balance连接测试出错: {e}")
    
    def system_message(self, message: str) -> Dict[str, str]:
        """创建系统消息"""
        return {"role": "system", "content": message}
    
    def user_message(self, message: str) -> Dict[str, str]:
        """创建用户消息"""
        return {"role": "user", "content": message}
    
    def assistant_message(self, message: str) -> Dict[str, str]:
        """创建助手消息"""
        return {"role": "assistant", "content": message}
    
    def submit_prompt(self, prompt, **kwargs) -> str:
        """
        向gemini-balance服务提交prompt并获取响应
        
        Args:
            prompt: 消息列表或字符串
            **kwargs: 额外参数
        
        Returns:
            str: 模型响应文本
        """
        
        # 处理prompt格式
        if isinstance(prompt, str):
            messages = [{"role": "user", "content": prompt}]
        elif isinstance(prompt, list):
            messages = prompt
        else:
            raise ValueError("prompt must be a string or list of messages")
        
        # 构建请求payload
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "stream": False
        }
        
        # 记录请求信息
        self.log(f"Gemini Balance请求参数:")
        self.log(f"模型: {self.model}")
        self.log(f"温度: {payload['temperature']}")
        self.log(f"最大tokens: {payload['max_tokens']}")
        self.log(f"消息数量: {len(messages)}")
        
        try:
            # 发送请求
            response = requests.post(
                f"{self.base_url}v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"Gemini Balance API错误: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail}"
                except:
                    error_msg += f" - {response.text}"
                raise Exception(error_msg)
            
            # 解析响应
            response_data = response.json()
            
            # 记录响应信息
            self.log(f"Gemini Balance响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 提取响应文本
            if "choices" in response_data and len(response_data["choices"]) > 0:
                content = response_data["choices"][0]["message"]["content"]
                return content
            else:
                raise Exception(f"无效的响应格式: {response_data}")
                
        except requests.exceptions.Timeout:
            raise Exception(f"Gemini Balance请求超时 (>{self.timeout}秒)")
        except requests.exceptions.ConnectionError:
            raise Exception(f"无法连接到Gemini Balance服务: {self.base_url}")
        except Exception as e:
            raise Exception(f"Gemini Balance请求失败: {str(e)}")
    
    def extract_sql(self, llm_response: str) -> str:
        """
        从LLM响应中提取SQL语句
        支持多种SQL语句类型和格式
        """
        if not llm_response:
            return ""
        
        # 清理响应
        llm_response = llm_response.strip()
        
        # 1. 首先尝试提取```sql代码块
        sql_block_patterns = [
            r"```sql\s*\n(.*?)```",
            r"```SQL\s*\n(.*?)```", 
            r"```\s*\n(.*?)```"
        ]
        
        for pattern in sql_block_patterns:
            match = re.search(pattern, llm_response, re.DOTALL | re.IGNORECASE)
            if match:
                sql = match.group(1).strip()
                if sql and self._is_valid_sql_start(sql):
                    return self._clean_sql(sql)
        
        # 2. 尝试提取常见的SQL语句
        sql_patterns = [
            r'\b(SELECT\s+.*?)(?=;|\n\n|$)',
            r'\b(SHOW\s+.*?)(?=;|\n\n|$)',
            r'\b(INSERT\s+.*?)(?=;|\n\n|$)',
            r'\b(UPDATE\s+.*?)(?=;|\n\n|$)',
            r'\b(DELETE\s+.*?)(?=;|\n\n|$)',
            r'\b(CREATE\s+.*?)(?=;|\n\n|$)',
            r'\b(DROP\s+.*?)(?=;|\n\n|$)',
            r'\b(ALTER\s+.*?)(?=;|\n\n|$)',
            r'\b(WITH\s+.*?)(?=;|\n\n|$)',
        ]
        
        for pattern in sql_patterns:
            match = re.search(pattern, llm_response, re.IGNORECASE | re.DOTALL)
            if match:
                sql = match.group(1).strip()
                return self._clean_sql(sql)
        
        # 3. 如果都没找到，检查整个响应是否就是SQL
        if self._is_valid_sql_start(llm_response):
            return self._clean_sql(llm_response)
        
        # 4. 返回原始响应
        return llm_response
    
    def _is_valid_sql_start(self, text: str) -> bool:
        """检查文本是否以有效的SQL关键字开始"""
        if not text:
            return False
        
        text = text.strip().upper()
        sql_keywords = [
            'SELECT', 'SHOW', 'INSERT', 'UPDATE', 'DELETE', 
            'CREATE', 'DROP', 'ALTER', 'WITH', 'EXPLAIN'
        ]
        
        return any(text.startswith(keyword) for keyword in sql_keywords)
    
    def _clean_sql(self, sql: str) -> str:
        """清理SQL语句"""
        if not sql:
            return ""
        
        # 移除首尾空白
        sql = sql.strip()
        
        # 移除末尾的分号
        if sql.endswith(';'):
            sql = sql[:-1].strip()
        
        # 移除可能的markdown标记
        sql = sql.replace('```', '').strip()
        
        return sql
