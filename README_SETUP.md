# Vanna AI SQL助手 - Ollama + MySQL 版本

这是一个配置好的Vanna AI SQL助手，使用Ollama本地模型和MySQL数据库。

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- <PERSON><PERSON><PERSON> (已安装并运行)
- MySQL数据库 (Docker或本地)
- 你的数据库配置:
  - 主机: localhost
  - 端口: 3306
  - 用户: root
  - 密码: 123qwe
  - 数据库: whodb_test

### 2. 安装

#### Linux/macOS:
```bash
chmod +x install.sh
./install.sh
```

#### Windows:
```cmd
install.bat
```

#### 手动安装:
```bash
# 安装Python依赖
pip install -r requirements.txt

# 确保Ollama正在运行
ollama serve

# 确保已下载模型
ollama pull llama3.2
```

### 3. 配置

编辑 `config.py` 文件来自定义设置:

```python
# 修改Ollama模型
OLLAMA_CONFIG = {
    'model': 'your-model-name:latest',  # 改成你的模型
    'ollama_host': 'http://localhost:11434',
    # ... 其他配置
}

# 修改数据库连接
MYSQL_CONFIG = {
    'host': 'your-host',
    'port': 3306,
    'user': 'your-user',
    'password': 'your-password',
    'database': 'your-database'
}
```

### 4. 启动

```bash
python start_vanna.py
```

## 💬 使用示例

启动后，你可以用自然语言提问:

```
💬 请输入你的问题: 显示所有表

💬 请输入你的问题: 用户表有多少条记录?

💬 请输入你的问题: 查询最近注册的10个用户

💬 请输入你的问题: What are the top 5 products by sales?
```

## 🔧 高级配置

### Ollama模型配置

在 `config.py` 中可以调整模型参数:

```python
OLLAMA_CONFIG = {
    'model': 'llama3.2:latest',
    'options': {
        'num_ctx': 4096,        # 上下文长度
        'temperature': 0.1,     # 创造性 (0-1)
        'top_p': 0.9,          # Top-p采样
        'repeat_penalty': 1.1,  # 重复惩罚
    }
}
```

### 数据库方言

支持的数据库类型:
- MySQL
- PostgreSQL
- SQLite
- SQL Server
- Oracle

在 `config.py` 中设置:
```python
GENERAL_CONFIG = {
    'dialect': 'MySQL',  # 改成你的数据库类型
}
```

## 🐛 故障排除

### 1. Ollama连接失败
```bash
# 检查Ollama是否运行
curl http://localhost:11434/api/version

# 启动Ollama
ollama serve

# 检查模型是否存在
ollama list
```

### 2. MySQL连接失败
```bash
# 检查MySQL是否运行
mysql -h localhost -u root -p123qwe whodb_test

# 如果使用Docker
docker ps | grep mysql
```

### 3. Python依赖问题
```bash
# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 或使用虚拟环境
python -m venv vanna_env
source vanna_env/bin/activate  # Linux/macOS
# 或
vanna_env\Scripts\activate.bat  # Windows
pip install -r requirements.txt
```

### 4. ChromaDB存储问题
```bash
# 删除ChromaDB数据重新开始
rm -rf chroma_db/
```

## 📁 文件说明

- `start_vanna.py` - 主启动脚本
- `config.py` - 配置文件
- `requirements.txt` - Python依赖
- `install.sh` / `install.bat` - 安装脚本
- `chroma_db/` - ChromaDB向量数据库存储目录

## 🎯 功能特性

- ✅ 支持中文和英文问答
- ✅ 自动学习数据库模式
- ✅ 本地Ollama模型，数据不出本地
- ✅ 持久化向量存储
- ✅ 智能SQL生成和执行
- ✅ 结果摘要生成
- ✅ 交互式问答界面

## 📝 注意事项

1. 首次运行会自动扫描数据库表结构进行训练
2. 训练数据存储在 `chroma_db/` 目录中
3. 可以随时重新训练或添加新的训练数据
4. 建议使用较大的Ollama模型以获得更好的效果

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
