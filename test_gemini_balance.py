#!/usr/bin/env python3
"""
测试Gemini Balance连接和功能
"""

import sys
import os
import requests
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_gemini_balance_service(base_url="http://localhost:8080/", api_key=""):
    """测试Gemini Balance服务连接"""
    print("🔍 测试Gemini Balance服务连接...")
    
    # 确保URL格式正确
    if not base_url.endswith('/'):
        base_url += '/'
    
    print(f"服务地址: {base_url}")
    
    # 准备请求头
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Vanna-Test/1.0"
    }
    
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    # 测试请求
    test_payload = {
        "model": "gemini-2.0-flash-exp",
        "messages": [
            {"role": "user", "content": "Hello! Please respond with 'Connection successful' if you can see this message."}
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    try:
        print("📡 发送测试请求...")
        response = requests.post(
            f"{base_url}v1/chat/completions",
            headers=headers,
            json=test_payload,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Gemini Balance服务连接成功!")
            
            if "choices" in response_data and len(response_data["choices"]) > 0:
                content = response_data["choices"][0]["message"]["content"]
                print(f"🤖 模型响应: {content}")
                
                # 显示详细信息
                if "model" in response_data:
                    print(f"📋 使用的模型: {response_data['model']}")
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用情况: {usage}")
                
                return True
            else:
                print("⚠️ 响应格式异常")
                print(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                return False
        else:
            print(f"❌ 服务连接失败: HTTP {response.status_code}")
            try:
                error_detail = response.json()
                print(f"错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到服务: {base_url}")
        print("请确保:")
        print("1. Gemini Balance服务正在运行")
        print("2. 服务地址正确")
        print("3. 网络连接正常")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_sql_generation():
    """测试SQL生成功能"""
    print("\n🔍 测试SQL生成功能...")
    
    try:
        from config_gemini import GEMINI_BALANCE_CONFIG, MYSQL_CONFIG, CHROMADB_CONFIG, GENERAL_CONFIG
    except ImportError:
        print("⚠️ 未找到config_gemini.py，使用默认配置")
        GEMINI_BALANCE_CONFIG = {
            'base_url': 'http://localhost:8080/',
            'model': 'gemini-2.0-flash-exp',
            'temperature': 0.1
        }
        MYSQL_CONFIG = {
            'host': 'localhost', 'port': 3306, 'user': 'root',
            'password': '123qwe', 'dbname': 'whodb_test'
        }
        CHROMADB_CONFIG = {'path': './chroma_db_gemini', 'client': 'persistent'}
        GENERAL_CONFIG = {'language': 'zh-CN', 'dialect': 'MySQL'}
    
    try:
        from vanna.gemini_balance.gemini_balance_chat import GeminiBalanceChat
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        
        # 创建测试实例
        config = {**GEMINI_BALANCE_CONFIG, **CHROMADB_CONFIG, **GENERAL_CONFIG}
        
        class TestVanna(ChromaDB_VectorStore, GeminiBalanceChat):
            def __init__(self, config):
                ChromaDB_VectorStore.__init__(self, config=config)
                GeminiBalanceChat.__init__(self, config=config)
        
        vn = TestVanna(config)
        
        # 测试简单的SQL生成
        print("📝 测试SQL生成...")
        test_question = "Generate a SQL query to count all records in a table called 'users'"
        
        # 创建简单的prompt
        prompt = [
            {"role": "system", "content": "You are a MySQL expert. Generate only the SQL query without explanations."},
            {"role": "user", "content": test_question}
        ]
        
        response = vn.submit_prompt(prompt)
        print(f"🤖 生成的响应: {response}")
        
        # 测试SQL提取
        extracted_sql = vn.extract_sql(response)
        print(f"📋 提取的SQL: {extracted_sql}")
        
        return True
        
    except Exception as e:
        print(f"❌ SQL生成测试失败: {e}")
        return False


def test_mysql_connection():
    """测试MySQL连接"""
    print("\n🔍 测试MySQL连接...")
    
    try:
        from config_gemini import MYSQL_CONFIG
    except ImportError:
        MYSQL_CONFIG = {
            'host': 'localhost', 'port': 3306, 'user': 'root',
            'password': '123qwe', 'dbname': 'whodb_test'
        }
    
    try:
        import pymysql
        
        conn = pymysql.connect(**MYSQL_CONFIG, cursorclass=pymysql.cursors.DictCursor)
        print("✅ MySQL连接成功!")
        
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📊 数据库中有 {len(tables)} 个表")
            if tables:
                print(f"📋 表列表: {[list(t.values())[0] for t in tables[:5]]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Gemini Balance + Vanna 连接测试")
    print("=" * 50)
    
    # 从配置文件读取设置
    try:
        from config_gemini import GEMINI_BALANCE_CONFIG
        base_url = GEMINI_BALANCE_CONFIG.get('base_url', 'http://localhost:8080/')
        api_key = GEMINI_BALANCE_CONFIG.get('api_key', '')
    except ImportError:
        print("⚠️ 未找到config_gemini.py，使用默认设置")
        base_url = 'http://localhost:8080/'
        api_key = ''
    
    # 运行测试
    tests = [
        ("Gemini Balance服务", lambda: test_gemini_balance_service(base_url, api_key)),
        ("MySQL连接", test_mysql_connection),
        ("SQL生成功能", test_sql_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "="*50)
    print("📋 测试结果汇总:")
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过! 可以运行: python3 start_vanna_gemini.py")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
        print("\n💡 配置提示:")
        print("1. 确保gemini-balance服务在运行")
        print("2. 检查config_gemini.py中的base_url设置")
        print("3. 确认MySQL数据库连接信息")


if __name__ == "__main__":
    main()
