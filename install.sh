#!/bin/bash

echo "🚀 Vanna AI SQL助手安装脚本"
echo "================================"

# 检查Python版本
echo "📋 检查Python环境..."
python_version=$(python3 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 找到Python: $python_version"
else
    echo "❌ 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查pip
echo "📋 检查pip..."
pip_version=$(pip3 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 找到pip: $pip_version"
else
    echo "❌ 未找到pip3，请先安装pip"
    exit 1
fi

# 创建虚拟环境（可选）
read -p "🤔 是否创建Python虚拟环境? (推荐) [y/N]: " create_venv
if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv vanna_env
    echo "🔄 激活虚拟环境..."
    source vanna_env/bin/activate
    echo "✅ 虚拟环境已创建并激活"
    echo "💡 下次使用前请运行: source vanna_env/bin/activate"
fi

# 安装依赖
echo "📦 安装Python依赖..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Python依赖安装完成"
else
    echo "❌ Python依赖安装失败"
    exit 1
fi

# 检查Ollama
echo "📋 检查Ollama服务..."
ollama_status=$(curl -s http://localhost:11434/api/version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Ollama服务正在运行"
    echo "📋 检查可用模型..."
    ollama list
else
    echo "⚠️ Ollama服务未运行或未安装"
    echo "请确保:"
    echo "1. 已安装Ollama: https://ollama.ai/"
    echo "2. Ollama服务正在运行: ollama serve"
    echo "3. 已下载模型: ollama pull llama3.2"
fi

# 检查MySQL连接
echo "📋 检查MySQL连接..."
python3 -c "
import pymysql
try:
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='123qwe',
        database='whodb_test'
    )
    print('✅ MySQL连接成功')
    conn.close()
except Exception as e:
    print(f'⚠️ MySQL连接失败: {e}')
    print('请确保:')
    print('1. MySQL服务正在运行')
    print('2. 数据库whodb_test存在')
    print('3. 用户root密码为123qwe')
"

echo ""
echo "🎉 安装完成!"
echo "================================"
echo "🚀 启动命令: python3 start_vanna.py"
echo "⚙️ 配置文件: config.py"
echo "📚 依赖文件: requirements.txt"
echo ""
echo "💡 使用提示:"
echo "- 首次运行会自动训练数据库模式"
echo "- 可以用中文或英文提问"
echo "- 输入'help'查看帮助"
echo "- 输入'quit'退出"
